#!/usr/bin/env python3
"""
Simple REST API server for Dream Portal - For Postman Testing
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import uuid
from datetime import datetime

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend integration

# In-memory storage (in production, use a database)
dreams_storage = [
    {
        "id": "1",
        "name": "Flying over mountains",
        "age": "Good",
        "type": "Lucid",
        "content": "I was soaring above beautiful snow-capped peaks",
        "created_at": "2025-01-15T10:00:00Z"
    },
    {
        "id": "2", 
        "name": "Lost in maze",
        "age": "Bad",
        "type": "Nightmare",
        "content": "I was trapped in endless dark corridors",
        "created_at": "2025-01-15T11:00:00Z"
    },
    {
        "id": "3",
        "name": "Meeting old friend",
        "age": "Good", 
        "type": "Memory",
        "content": "I met my childhood friend in a beautiful garden",
        "created_at": "2025-01-15T12:00:00Z"
    },
    {
        "id": "4",
        "name": "Test automation",
        "age": "Test",
        "type": "Work",
        "content": "I was writing automated tests for a dream portal",
        "created_at": "2025-01-15T13:00:00Z"
    }
]

@app.route('/', methods=['GET'])
def home():
    """API Home endpoint"""
    return jsonify({
        "message": "Dream Portal API",
        "version": "1.0.0",
        "endpoints": {
            "GET /api/dreams": "Get all dreams",
            "POST /api/dreams": "Create a new dream",
            "GET /api/dreams/{id}": "Get specific dream",
            "PUT /api/dreams/{id}": "Update dream",
            "DELETE /api/dreams/{id}": "Delete dream",
            "GET /api/dreams/stats": "Get dream statistics",
            "POST /api/dreams/classify": "AI classify dream content"
        }
    })

@app.route('/api/dreams', methods=['GET'])
def get_dreams():
    """Get all dreams with optional filtering"""
    dream_type = request.args.get('type')
    age = request.args.get('age')
    
    filtered_dreams = dreams_storage.copy()
    
    if dream_type:
        filtered_dreams = [d for d in filtered_dreams if d['type'].lower() == dream_type.lower()]
    
    if age:
        filtered_dreams = [d for d in filtered_dreams if d['age'].lower() == age.lower()]
    
    return jsonify({
        "dreams": filtered_dreams,
        "total": len(filtered_dreams),
        "filters_applied": {
            "type": dream_type,
            "age": age
        }
    })

@app.route('/api/dreams', methods=['POST'])
def create_dream():
    """Create a new dream"""
    data = request.get_json()
    
    # Validation
    required_fields = ['name', 'age', 'type', 'content']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"Missing required field: {field}"}), 400
    
    # Create new dream
    new_dream = {
        "id": str(uuid.uuid4()),
        "name": data['name'],
        "age": data['age'],
        "type": data['type'],
        "content": data['content'],
        "created_at": datetime.utcnow().isoformat() + "Z"
    }
    
    dreams_storage.append(new_dream)
    
    return jsonify({
        "message": "Dream created successfully",
        "dream": new_dream
    }), 201

@app.route('/api/dreams/<dream_id>', methods=['GET'])
def get_dream(dream_id):
    """Get a specific dream by ID"""
    dream = next((d for d in dreams_storage if d['id'] == dream_id), None)
    
    if not dream:
        return jsonify({"error": "Dream not found"}), 404
    
    return jsonify({"dream": dream})

@app.route('/api/dreams/<dream_id>', methods=['PUT'])
def update_dream(dream_id):
    """Update a specific dream"""
    dream = next((d for d in dreams_storage if d['id'] == dream_id), None)
    
    if not dream:
        return jsonify({"error": "Dream not found"}), 404
    
    data = request.get_json()
    
    # Update fields
    for field in ['name', 'age', 'type', 'content']:
        if field in data:
            dream[field] = data[field]
    
    dream['updated_at'] = datetime.utcnow().isoformat() + "Z"
    
    return jsonify({
        "message": "Dream updated successfully",
        "dream": dream
    })

@app.route('/api/dreams/<dream_id>', methods=['DELETE'])
def delete_dream(dream_id):
    """Delete a specific dream"""
    global dreams_storage
    
    dream = next((d for d in dreams_storage if d['id'] == dream_id), None)
    
    if not dream:
        return jsonify({"error": "Dream not found"}), 404
    
    dreams_storage = [d for d in dreams_storage if d['id'] != dream_id]
    
    return jsonify({"message": "Dream deleted successfully"})

@app.route('/api/dreams/stats', methods=['GET'])
def get_dream_stats():
    """Get dream statistics"""
    total_dreams = len(dreams_storage)
    good_dreams = len([d for d in dreams_storage if d['age'] == 'Good'])
    bad_dreams = len([d for d in dreams_storage if d['age'] == 'Bad'])
    test_dreams = len([d for d in dreams_storage if d['age'] == 'Test'])
    
    # Dream types distribution
    types_count = {}
    for dream in dreams_storage:
        dream_type = dream['type']
        types_count[dream_type] = types_count.get(dream_type, 0) + 1
    
    return jsonify({
        "total_dreams": total_dreams,
        "by_age": {
            "good": good_dreams,
            "bad": bad_dreams,
            "test": test_dreams
        },
        "by_type": types_count,
        "percentages": {
            "good": round((good_dreams / total_dreams) * 100, 1) if total_dreams > 0 else 0,
            "bad": round((bad_dreams / total_dreams) * 100, 1) if total_dreams > 0 else 0,
            "test": round((test_dreams / total_dreams) * 100, 1) if total_dreams > 0 else 0
        }
    })

@app.route('/api/dreams/classify', methods=['POST'])
def classify_dream():
    """AI-powered dream classification (mock endpoint)"""
    data = request.get_json()
    
    if 'content' not in data:
        return jsonify({"error": "Missing 'content' field"}), 400
    
    content = data['content'].lower()
    
    # Simple rule-based classification (mock AI)
    if any(word in content for word in ['flying', 'soaring', 'peaceful', 'beautiful']):
        sentiment = 'positive'
        category = 'lucid'
        themes = ['freedom', 'peace', 'adventure']
        coherence_score = 8
        emotional_intensity = 6
    elif any(word in content for word in ['scared', 'nightmare', 'trapped', 'dark']):
        sentiment = 'negative'
        category = 'nightmare'
        themes = ['fear', 'anxiety', 'darkness']
        coherence_score = 7
        emotional_intensity = 9
    else:
        sentiment = 'neutral'
        category = 'memory'
        themes = ['general', 'everyday']
        coherence_score = 6
        emotional_intensity = 5
    
    return jsonify({
        "classification": {
            "sentiment": sentiment,
            "category": category,
            "themes": themes,
            "coherence_score": coherence_score,
            "emotional_intensity": emotional_intensity,
            "confidence": 0.85
        },
        "original_content": data['content']
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "dreams_count": len(dreams_storage)
    })

if __name__ == '__main__':
    print("🚀 Starting Dream Portal API Server...")
    print("📡 API will be available at: http://localhost:5000")
    print("📖 API Documentation: http://localhost:5000")
    print("🧪 Ready for Postman testing!")
    app.run(debug=True, host='0.0.0.0', port=5000)
