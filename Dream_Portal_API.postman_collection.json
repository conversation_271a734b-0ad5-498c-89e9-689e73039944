{"info": {"_postman_id": "dream-portal-api-collection", "name": "Dream Portal API", "description": "Complete API collection for testing Dream Portal REST endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}, "description": "Check if the API server is running and healthy"}, "response": []}, {"name": "Get All Dreams", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/dreams", "host": ["{{base_url}}"], "path": ["api", "dreams"]}, "description": "Retrieve all dreams from the database"}, "response": []}, {"name": "Get Dreams - Filter by Type", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/dreams?type=Lucid", "host": ["{{base_url}}"], "path": ["api", "dreams"], "query": [{"key": "type", "value": "Lucid", "description": "Filter by dream type (Lucid, <PERSON>, <PERSON>, etc.)"}]}, "description": "Get dreams filtered by type"}, "response": []}, {"name": "Get Dreams - Filter by Age", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/dreams?age=Good", "host": ["{{base_url}}"], "path": ["api", "dreams"], "query": [{"key": "age", "value": "Good", "description": "Filter by dream age (Good, Bad, Test)"}]}, "description": "Get dreams filtered by age category"}, "response": []}, {"name": "Create New Dream", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Flying Through Clouds\",\n    \"age\": \"Good\",\n    \"type\": \"Lucid\",\n    \"content\": \"I was soaring through fluffy white clouds, feeling completely free and peaceful. The sun was shining and I could see the entire world below me.\"\n}"}, "url": {"raw": "{{base_url}}/api/dreams", "host": ["{{base_url}}"], "path": ["api", "dreams"]}, "description": "Create a new dream entry"}, "response": []}, {"name": "Get Specific Dream", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/dreams/{{dream_id}}", "host": ["{{base_url}}"], "path": ["api", "dreams", "{{dream_id}}"]}, "description": "Get a specific dream by ID (replace {{dream_id}} with actual ID)"}, "response": []}, {"name": "Update Dream", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Dream Name\",\n    \"content\": \"This dream content has been updated via API\"\n}"}, "url": {"raw": "{{base_url}}/api/dreams/{{dream_id}}", "host": ["{{base_url}}"], "path": ["api", "dreams", "{{dream_id}}"]}, "description": "Update an existing dream (replace {{dream_id}} with actual ID)"}, "response": []}, {"name": "Delete Dream", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/dreams/{{dream_id}}", "host": ["{{base_url}}"], "path": ["api", "dreams", "{{dream_id}}"]}, "description": "Delete a specific dream (replace {{dream_id}} with actual ID)"}, "response": []}, {"name": "Get Dream Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/dreams/stats", "host": ["{{base_url}}"], "path": ["api", "dreams", "stats"]}, "description": "Get comprehensive dream statistics and analytics"}, "response": []}, {"name": "AI Dream Classification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"I was flying over beautiful mountains, feeling completely free and peaceful. The landscape below was breathtaking with snow-capped peaks and green valleys.\"\n}"}, "url": {"raw": "{{base_url}}/api/dreams/classify", "host": ["{{base_url}}"], "path": ["api", "dreams", "classify"]}, "description": "Use AI to classify and analyze dream content"}, "response": []}, {"name": "API Documentation", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Get API documentation and available endpoints"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "dream_id", "value": "1", "type": "string"}]}