{"info": {"name": "Dream Portal API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Dreams", "request": {"method": "GET", "url": "{{base_url}}/api/dreams"}}, {"name": "Create Dream", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Dream\",\n    \"age\": \"Good\",\n    \"type\": \"Lucid\",\n    \"content\": \"Flying over mountains\"\n}"}, "url": "{{base_url}}/api/dreams"}}, {"name": "Get Dream by ID", "request": {"method": "GET", "url": "{{base_url}}/api/dreams/{{dream_id}}"}}, {"name": "Update Dream", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Dream\",\n    \"content\": \"Updated content\"\n}"}, "url": "{{base_url}}/api/dreams/{{dream_id}}"}}, {"name": "Delete Dream", "request": {"method": "DELETE", "url": "{{base_url}}/api/dreams/{{dream_id}}"}}, {"name": "Get Statistics", "request": {"method": "GET", "url": "{{base_url}}/api/dreams/stats"}}], "variable": [{"key": "base_url", "value": "http://localhost:5000"}, {"key": "dream_id", "value": "1"}]}