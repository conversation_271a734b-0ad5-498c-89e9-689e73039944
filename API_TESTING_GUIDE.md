# 🚀 Complete API Testing Guide - Dream Portal

## 📋 **What You Now Have**

I've added a complete REST API backend to your Dream Portal project! Here's what's available:

### 🌐 **API Endpoints for Postman Testing**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/health` | Health check |
| `GET` | `/api/dreams` | Get all dreams |
| `GET` | `/api/dreams?type=Lucid` | Filter dreams by type |
| `GET` | `/api/dreams?age=Good` | Filter dreams by age |
| `POST` | `/api/dreams` | Create new dream |
| `GET` | `/api/dreams/{id}` | Get specific dream |
| `PUT` | `/api/dreams/{id}` | Update dream |
| `DELETE` | `/api/dreams/{id}` | Delete dream |
| `GET` | `/api/dreams/stats` | Get statistics |
| `POST` | `/api/dreams/classify` | AI dream classification |

## 🔧 **Step-by-Step Setup**

### 1. Install Dependencies

```bash
# Install all required packages
pip install -r requirements.txt
```

### 2. Start the API Server

```bash
# Start the REST API server
python api_server.py
```

You should see:
```
🚀 Starting Dream Portal API Server...
📡 API will be available at: http://localhost:5000
📖 API Documentation: http://localhost:5000
🧪 Ready for Postman testing!
```

### 3. Start the Website (Optional)

```bash
# In another terminal, start the website
python start_server.py --open
```

## 📮 **Postman Testing**

### Import the Collection

1. **Download Postman** from https://www.postman.com/downloads/
2. **Import the collection**:
   - Open Postman
   - Click "Import"
   - Select `Dream_Portal_API.postman_collection.json`
   - The collection will appear in your workspace

### 🧪 **Test Scenarios**

#### **1. Health Check**
```
GET http://localhost:5000/api/health
```
**Expected Response:**
```json
{
    "status": "healthy",
    "timestamp": "2025-01-15T10:00:00Z",
    "dreams_count": 4
}
```

#### **2. Get All Dreams**
```
GET http://localhost:5000/api/dreams
```
**Expected Response:**
```json
{
    "dreams": [
        {
            "id": "1",
            "name": "Flying over mountains",
            "age": "Good",
            "type": "Lucid",
            "content": "I was soaring above beautiful snow-capped peaks",
            "created_at": "2025-01-15T10:00:00Z"
        }
    ],
    "total": 4
}
```

#### **3. Create New Dream**
```
POST http://localhost:5000/api/dreams
Content-Type: application/json

{
    "name": "My Test Dream",
    "age": "Good",
    "type": "Adventure",
    "content": "I was exploring a magical forest with talking animals"
}
```

#### **4. Filter Dreams**
```
GET http://localhost:5000/api/dreams?type=Lucid
GET http://localhost:5000/api/dreams?age=Good
```

#### **5. AI Classification**
```
POST http://localhost:5000/api/dreams/classify
Content-Type: application/json

{
    "content": "I was flying over beautiful mountains feeling peaceful"
}
```

**Expected Response:**
```json
{
    "classification": {
        "sentiment": "positive",
        "category": "lucid",
        "themes": ["freedom", "peace", "adventure"],
        "coherence_score": 8,
        "emotional_intensity": 6,
        "confidence": 0.85
    }
}
```

## 🧪 **Running Automated API Tests**

### Run API Tests with Pytest

```bash
# Run only API tests
python run_tests.py --type api

# Or directly with pytest
pytest tests/api/ -v
```

### Run All Tests (UI + API)

```bash
# Run everything
python run_tests.py --type all --verbose
```

## 📊 **Complete Testing Workflow**

### 1. **Start Both Servers**

**Terminal 1 - API Server:**
```bash
python api_server.py
```

**Terminal 2 - Website Server:**
```bash
python start_server.py
```

### 2. **Manual Testing in Postman**

1. Import the collection
2. Test each endpoint
3. Verify responses
4. Try error scenarios

### 3. **Automated Testing**

```bash
# Run all automated tests
python run_tests.py --verbose
```

## 🎯 **Key Test Scenarios for Postman**

### **Positive Test Cases**

1. ✅ **Create Dream** - Valid data
2. ✅ **Get Dreams** - Retrieve all
3. ✅ **Filter Dreams** - By type/age
4. ✅ **Update Dream** - Modify existing
5. ✅ **Delete Dream** - Remove entry
6. ✅ **Get Stats** - Analytics data
7. ✅ **AI Classify** - Content analysis

### **Negative Test Cases**

1. ❌ **Create Dream** - Missing required fields
2. ❌ **Get Dream** - Invalid ID
3. ❌ **Update Dream** - Non-existent ID
4. ❌ **Delete Dream** - Already deleted
5. ❌ **AI Classify** - Empty content

### **Edge Cases**

1. 🔍 **Large Content** - Very long dream descriptions
2. 🔍 **Special Characters** - Unicode, emojis
3. 🔍 **Concurrent Requests** - Multiple simultaneous calls

## 🌐 **API URLs for Manual Testing**

### Base URL
```
http://localhost:5000
```

### Quick Test URLs
```bash
# Health check
curl http://localhost:5000/api/health

# Get all dreams
curl http://localhost:5000/api/dreams

# Get statistics
curl http://localhost:5000/api/dreams/stats

# Create dream
curl -X POST http://localhost:5000/api/dreams \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Dream","age":"Good","type":"Test","content":"API test"}'
```

## 📈 **What You Can Test**

### **Functional Testing**
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Data validation
- ✅ Filtering and searching
- ✅ Error handling

### **Integration Testing**
- ✅ API + Database interactions
- ✅ AI classification integration
- ✅ Cross-endpoint workflows

### **Performance Testing**
- ✅ Response times
- ✅ Concurrent users
- ✅ Large data sets

## 🎉 **You Now Have Complete Testing Coverage**

1. **🌐 Website UI Testing** - Selenium automation
2. **📡 REST API Testing** - Postman + automated tests
3. **🤖 AI Integration Testing** - OpenAI classification
4. **📊 Comprehensive Reporting** - HTML reports with screenshots

## 🚀 **Next Steps**

1. **Import the Postman collection**
2. **Start the API server** (`python api_server.py`)
3. **Test all endpoints** in Postman
4. **Run automated tests** (`python run_tests.py`)
5. **Check the HTML reports** in `reports/` folder

You now have a complete, professional-grade testing suite that covers both UI and API testing! 🎯
