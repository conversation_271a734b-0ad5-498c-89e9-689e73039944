"""
API tests for Dream Portal REST API endpoints
"""
import pytest
import requests
import json
from config.test_config import TestConfig


class TestDreamsAPI:
    """Test cases for Dreams API endpoints"""
    
    BASE_URL = "http://localhost:5000"
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for each test"""
        # Check if API server is running
        try:
            response = requests.get(f"{self.BASE_URL}/api/health", timeout=5)
            if response.status_code != 200:
                pytest.skip("API server not running. Start with: python api_server.py")
        except requests.exceptions.RequestException:
            pytest.skip("API server not accessible. Start with: python api_server.py")
    
    @pytest.mark.api
    def test_api_health_check(self):
        """Test API health check endpoint"""
        response = requests.get(f"{self.BASE_URL}/api/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "dreams_count" in data
    
    @pytest.mark.api
    def test_get_all_dreams(self):
        """Test GET /api/dreams endpoint"""
        response = requests.get(f"{self.BASE_URL}/api/dreams")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "dreams" in data
        assert "total" in data
        assert isinstance(data["dreams"], list)
        assert data["total"] >= 0
        
        # Check dream structure
        if data["dreams"]:
            dream = data["dreams"][0]
            required_fields = ["id", "name", "age", "type", "content", "created_at"]
            for field in required_fields:
                assert field in dream, f"Missing field: {field}"
    
    @pytest.mark.api
    def test_get_dreams_with_filter(self):
        """Test GET /api/dreams with filters"""
        # Test type filter
        response = requests.get(f"{self.BASE_URL}/api/dreams?type=Lucid")
        assert response.status_code == 200
        data = response.json()
        
        # All returned dreams should have type "Lucid"
        for dream in data["dreams"]:
            assert dream["type"] == "Lucid"
        
        # Test age filter
        response = requests.get(f"{self.BASE_URL}/api/dreams?age=Good")
        assert response.status_code == 200
        data = response.json()
        
        # All returned dreams should have age "Good"
        for dream in data["dreams"]:
            assert dream["age"] == "Good"
    
    @pytest.mark.api
    def test_create_dream(self):
        """Test POST /api/dreams endpoint"""
        new_dream = {
            "name": "API Test Dream",
            "age": "Good",
            "type": "Test",
            "content": "This is a test dream created via API"
        }
        
        response = requests.post(
            f"{self.BASE_URL}/api/dreams",
            json=new_dream,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["message"] == "Dream created successfully"
        assert "dream" in data
        
        created_dream = data["dream"]
        assert created_dream["name"] == new_dream["name"]
        assert created_dream["age"] == new_dream["age"]
        assert created_dream["type"] == new_dream["type"]
        assert created_dream["content"] == new_dream["content"]
        assert "id" in created_dream
        assert "created_at" in created_dream
    
    @pytest.mark.api
    def test_create_dream_validation(self):
        """Test POST /api/dreams validation"""
        # Test missing required field
        invalid_dream = {
            "name": "Test Dream",
            "age": "Good"
            # Missing 'type' and 'content'
        }
        
        response = requests.post(
            f"{self.BASE_URL}/api/dreams",
            json=invalid_dream,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "error" in data
        assert "Missing required field" in data["error"]
    
    @pytest.mark.api
    def test_get_specific_dream(self):
        """Test GET /api/dreams/{id} endpoint"""
        # First, get all dreams to find a valid ID
        response = requests.get(f"{self.BASE_URL}/api/dreams")
        dreams = response.json()["dreams"]
        
        if dreams:
            dream_id = dreams[0]["id"]
            
            # Get specific dream
            response = requests.get(f"{self.BASE_URL}/api/dreams/{dream_id}")
            assert response.status_code == 200
            
            data = response.json()
            assert "dream" in data
            assert data["dream"]["id"] == dream_id
    
    @pytest.mark.api
    def test_get_nonexistent_dream(self):
        """Test GET /api/dreams/{id} with invalid ID"""
        response = requests.get(f"{self.BASE_URL}/api/dreams/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        assert data["error"] == "Dream not found"
    
    @pytest.mark.api
    def test_update_dream(self):
        """Test PUT /api/dreams/{id} endpoint"""
        # First create a dream to update
        new_dream = {
            "name": "Dream to Update",
            "age": "Good",
            "type": "Test",
            "content": "Original content"
        }
        
        create_response = requests.post(
            f"{self.BASE_URL}/api/dreams",
            json=new_dream,
            headers={"Content-Type": "application/json"}
        )
        
        dream_id = create_response.json()["dream"]["id"]
        
        # Update the dream
        update_data = {
            "name": "Updated Dream Name",
            "content": "Updated content"
        }
        
        response = requests.put(
            f"{self.BASE_URL}/api/dreams/{dream_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Dream updated successfully"
        updated_dream = data["dream"]
        assert updated_dream["name"] == update_data["name"]
        assert updated_dream["content"] == update_data["content"]
        assert "updated_at" in updated_dream
    
    @pytest.mark.api
    def test_delete_dream(self):
        """Test DELETE /api/dreams/{id} endpoint"""
        # First create a dream to delete
        new_dream = {
            "name": "Dream to Delete",
            "age": "Test",
            "type": "Test",
            "content": "This dream will be deleted"
        }
        
        create_response = requests.post(
            f"{self.BASE_URL}/api/dreams",
            json=new_dream,
            headers={"Content-Type": "application/json"}
        )
        
        dream_id = create_response.json()["dream"]["id"]
        
        # Delete the dream
        response = requests.delete(f"{self.BASE_URL}/api/dreams/{dream_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Dream deleted successfully"
        
        # Verify dream is deleted
        get_response = requests.get(f"{self.BASE_URL}/api/dreams/{dream_id}")
        assert get_response.status_code == 404
    
    @pytest.mark.api
    def test_get_dream_stats(self):
        """Test GET /api/dreams/stats endpoint"""
        response = requests.get(f"{self.BASE_URL}/api/dreams/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        required_fields = ["total_dreams", "by_age", "by_type", "percentages"]
        for field in required_fields:
            assert field in data
        
        # Check by_age structure
        assert "good" in data["by_age"]
        assert "bad" in data["by_age"]
        assert "test" in data["by_age"]
        
        # Check percentages
        assert "good" in data["percentages"]
        assert "bad" in data["percentages"]
        assert "test" in data["percentages"]
        
        # Verify percentages are valid
        for percentage in data["percentages"].values():
            assert 0 <= percentage <= 100
    
    @pytest.mark.api
    def test_classify_dream(self):
        """Test POST /api/dreams/classify endpoint"""
        test_content = "I was flying over beautiful mountains feeling peaceful and free"
        
        response = requests.post(
            f"{self.BASE_URL}/api/dreams/classify",
            json={"content": test_content},
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "classification" in data
        classification = data["classification"]
        
        required_fields = ["sentiment", "category", "themes", "coherence_score", "emotional_intensity", "confidence"]
        for field in required_fields:
            assert field in classification
        
        # Verify data types and ranges
        assert classification["sentiment"] in ["positive", "negative", "neutral"]
        assert isinstance(classification["themes"], list)
        assert 1 <= classification["coherence_score"] <= 10
        assert 1 <= classification["emotional_intensity"] <= 10
        assert 0 <= classification["confidence"] <= 1
    
    @pytest.mark.api
    def test_classify_dream_validation(self):
        """Test POST /api/dreams/classify validation"""
        # Test missing content
        response = requests.post(
            f"{self.BASE_URL}/api/dreams/classify",
            json={},
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "error" in data
        assert "Missing 'content' field" in data["error"]
